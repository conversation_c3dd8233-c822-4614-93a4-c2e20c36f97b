/**
 * 边缘计算扩展面板演示页面
 * 集成批次5：边缘计算扩展面板（59个节点）功能演示
 */

import React from 'react';
import { Card, Typography, Space, Alert, Divider } from 'antd';
import {
  CloudServerOutlined,
  ApiOutlined,
  WifiOutlined,
  MobileOutlined,
  BrainOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import EdgeComputingExtendedPanelWrapper from '../components/visual-script/panels/EdgeComputingExtendedPanelWrapper';

const { Title, Paragraph, Text } = Typography;

/**
 * 边缘计算扩展面板演示页面组件
 */
export const EdgeComputingExtendedDemo: React.FC = () => {
  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题 */}
      <Card style={{ marginBottom: 24 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Title level={2}>
            <Space>
              <CloudServerOutlined style={{ color: '#1890ff' }} />
              集成批次5：边缘计算扩展面板（59个节点）
            </Space>
          </Title>
          
          <Alert
            message="集成完成"
            description="边缘计算扩展面板已成功集成到编辑器，包含5个子面板共59个节点，支持完整的拖拽和属性编辑功能。"
            type="success"
            showIcon
            icon={<CheckCircleOutlined />}
          />

          <Divider />

          {/* 功能特性 */}
          <Title level={4}>🎯 核心功能特性</Title>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
            <Card size="small" style={{ border: '1px solid #fa8c16' }}>
              <Space>
                <MobileOutlined style={{ color: '#fa8c16', fontSize: '20px' }} />
                <div>
                  <Text strong>边缘设备管理（25个节点）</Text>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    设备注册、监控、控制、资源管理、网络连接、安全防护、更新维护等
                  </div>
                </div>
              </Space>
            </Card>

            <Card size="small" style={{ border: '1px solid #eb2f96' }}>
              <Space>
                <BrainOutlined style={{ color: '#eb2f96', fontSize: '20px' }} />
                <div>
                  <Text strong>边缘AI（12个节点）</Text>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    AI推理、模型部署、模型优化、联邦学习、AI监控、性能分析等
                  </div>
                </div>
              </Space>
            </Card>

            <Card size="small" style={{ border: '1px solid #52c41a' }}>
              <Space>
                <CloudServerOutlined style={{ color: '#52c41a', fontSize: '20px' }} />
                <div>
                  <Text strong>云边协调（8个节点）</Text>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    云边编排、混合计算、数据同步、任务分发、资源优化等
                  </div>
                </div>
              </Space>
            </Card>

            <Card size="small" style={{ border: '1px solid #1890ff' }}>
              <Space>
                <ApiOutlined style={{ color: '#1890ff', fontSize: '20px' }} />
                <div>
                  <Text strong>边缘路由（6个节点）</Text>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    边缘路由、负载均衡、缓存、压缩、优化、服务质量等
                  </div>
                </div>
              </Space>
            </Card>

            <Card size="small" style={{ border: '1px solid #722ed1' }}>
              <Space>
                <WifiOutlined style={{ color: '#722ed1', fontSize: '20px' }} />
                <div>
                  <Text strong>5G网络（8个节点）</Text>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    5G连接、网络切片、MEC、波束成形、MIMO、URLLC、eMBB、mMTC等
                  </div>
                </div>
              </Space>
            </Card>
          </div>

          <Divider />

          {/* 技术亮点 */}
          <Title level={4}>✨ 技术亮点</Title>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '12px' }}>
            <Card size="small">
              <Text strong>🎯 拖拽交互</Text>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                支持节点拖拽到画布，实时预览和位置定位
              </div>
            </Card>

            <Card size="small">
              <Text strong>⚙️ 属性编辑</Text>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                完整的节点属性编辑界面，支持网络、资源、安全等配置
              </div>
            </Card>

            <Card size="small">
              <Text strong>🔍 智能搜索</Text>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                支持节点名称、描述、标签的模糊搜索和分类过滤
              </div>
            </Card>

            <Card size="small">
              <Text strong>📊 实时统计</Text>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                实时显示各类节点数量和使用情况统计
              </div>
            </Card>

            <Card size="small">
              <Text strong>🎨 视觉设计</Text>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                统一的视觉设计语言，清晰的分类标识和状态反馈
              </div>
            </Card>

            <Card size="small">
              <Text strong>🔧 扩展性</Text>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                模块化设计，支持后续节点类型的快速扩展
              </div>
            </Card>
          </div>

          <Divider />

          {/* 使用说明 */}
          <Title level={4}>📖 使用说明</Title>
          <div style={{ background: '#fafafa', padding: '16px', borderRadius: '8px' }}>
            <ol style={{ margin: 0, paddingLeft: '20px' }}>
              <li style={{ marginBottom: '8px' }}>
                <Text strong>浏览节点</Text>：在左侧面板中浏览不同类别的边缘计算节点
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Text strong>搜索过滤</Text>：使用搜索框或分类按钮快速找到需要的节点
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Text strong>拖拽添加</Text>：将节点拖拽到右侧画布区域进行添加
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Text strong>属性配置</Text>：点击节点信息按钮查看和编辑节点属性
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Text strong>画布管理</Text>：在画布中查看已添加的节点，支持清空操作
              </li>
            </ol>
          </div>
        </Space>
      </Card>

      {/* 演示组件 */}
      <EdgeComputingExtendedPanelWrapper />

      {/* 技术说明 */}
      <Card style={{ marginTop: 24 }}>
        <Title level={4}>🔧 技术实现</Title>
        <Paragraph>
          本次集成基于React + TypeScript + Ant Design技术栈，采用以下关键技术：
        </Paragraph>
        <ul>
          <li><Text code>react-dnd</Text>：实现拖拽功能，支持节点从面板拖拽到画布</li>
          <li><Text code>Ant Design</Text>：提供统一的UI组件和设计语言</li>
          <li><Text code>TypeScript</Text>：确保类型安全和代码质量</li>
          <li><Text code>模块化设计</Text>：组件高度解耦，支持独立开发和测试</li>
          <li><Text code>状态管理</Text>：使用React Hooks进行状态管理和事件处理</li>
          <li><Text code>响应式布局</Text>：支持不同屏幕尺寸的自适应显示</li>
        </ul>

        <Divider />

        <Title level={5}>📁 文件结构</Title>
        <div style={{ background: '#f6f8fa', padding: '12px', borderRadius: '6px', fontFamily: 'monospace', fontSize: '12px' }}>
          <div>editor/src/components/visual-script/</div>
          <div>├── panels/</div>
          <div>│   ├── EdgeComputingNodesPanel.tsx          # 边缘计算节点面板（已扩展）</div>
          <div>│   ├── EdgeComputingExtendedPanelWrapper.tsx # 扩展面板包装器</div>
          <div>└── nodes/</div>
          <div>    ├── EdgeComputingNodeDragDrop.tsx        # 拖拽功能组件</div>
          <div>    ├── EdgeComputingNodePropertyEditor.tsx  # 属性编辑器组件</div>
          <div>    └── EdgeComputingNodesIntegration.ts     # 节点集成逻辑</div>
        </div>

        <Divider />

        <Alert
          message="集成状态更新"
          description="已更新《视觉脚本系统节点开发方案_重新扫描更新版.md》文档，集成批次5标记为已完成，节点集成进度从75.1%（511个）提升至83.8%（570个）。"
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      </Card>
    </div>
  );
};

export default EdgeComputingExtendedDemo;
