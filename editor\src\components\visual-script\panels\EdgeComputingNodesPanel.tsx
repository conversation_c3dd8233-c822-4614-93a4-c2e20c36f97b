/**
 * 边缘计算节点面板
 * 批次0.1：提供46个边缘计算节点的分类展示和集成功能
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Collapse,
  List,
  Input,
  Space,
  Tag,
  Typography,
  Badge,
  Divider,
  Spin,
  Tooltip,
  Button,
  message
} from 'antd';
import {
  SearchOutlined,
  CloudServerOutlined,
  ApiOutlined,
  WifiOutlined,
  SettingOutlined,
  FilterOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  RouterOutlined,
  CloudOutlined,
  MobileOutlined,
  BrainOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { EdgeComputingNodesIntegration } from '../nodes/EdgeComputingNodesIntegration';
import { EdgeComputingNodeDragDrop, EdgeComputingDragItem } from '../nodes/EdgeComputingNodeDragDrop';
import { EdgeComputingNodePropertyEditor, EdgeComputingNodeProperties } from '../nodes/EdgeComputingNodePropertyEditor';

const { Text, Title } = Typography;
const { Panel } = Collapse;
const { Search } = Input;

/**
 * 边缘计算节点分类
 */
export enum EdgeComputingNodeCategory {
  EDGE_ROUTING = 'Edge/Routing',
  CLOUD_EDGE = 'Edge/CloudEdge',
  FIVE_G = 'Edge/5G',
  EDGE_DEVICE = 'Edge/Device',
  EDGE_AI = 'Edge/AI'
}

/**
 * 边缘计算节点面板属性
 */
export interface EdgeComputingNodesPanelProps {
  integration?: EdgeComputingNodesIntegration;
  onNodeSelect?: (nodeType: string) => void;
  onNodeAdd?: (nodeType: string) => void;
  onNodeDrop?: (node: EdgeComputingDragItem, position: { x: number; y: number }) => void;
  onNodePropertiesChange?: (nodeType: string, properties: EdgeComputingNodeProperties) => void;
  visible?: boolean;
  loading?: boolean;
  showPropertyEditor?: boolean;
}

/**
 * 节点项接口
 */
interface NodeItem {
  type: string;
  name: string;
  description: string;
  category: EdgeComputingNodeCategory;
  icon: string;
  color: string;
  tags: string[];
}

export const EdgeComputingNodesPanel: React.FC<EdgeComputingNodesPanelProps> = ({
  onNodeSelect,
  onNodeAdd,
  onNodeDrop,
  onNodePropertiesChange,
  visible = true,
  loading = false,
  showPropertyEditor = false
}) => {
  const { t } = useTranslation();
  const [expandedPanels, setExpandedPanels] = useState<string[]>([]);
  const [nodeItems, setNodeItems] = useState<NodeItem[]>([]);
  const [filteredNodes, setFilteredNodes] = useState<NodeItem[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedNode, setSelectedNode] = useState<NodeItem | null>(null);
  const [showProperties, setShowProperties] = useState(false);
  const [nodeProperties, setNodeProperties] = useState<EdgeComputingNodeProperties | null>(null);

  // 初始化节点数据
  useEffect(() => {
    const initializeNodes = () => {
      const nodes: NodeItem[] = [
        // 边缘路由节点 (6个)
        {
          type: 'EdgeRoutingNode',
          name: '边缘路由',
          description: '提供智能边缘路由决策功能',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '🔀',
          color: '#1890ff',
          tags: ['路由', '智能']
        },
        {
          type: 'EdgeLoadBalancingNode',
          name: '边缘负载均衡',
          description: '实现边缘节点间的负载均衡',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '⚖️',
          color: '#1890ff',
          tags: ['负载均衡', '性能']
        },
        {
          type: 'EdgeCachingNode',
          name: '边缘缓存',
          description: '提供边缘缓存管理功能',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '💾',
          color: '#1890ff',
          tags: ['缓存', '存储']
        },
        {
          type: 'EdgeCompressionNode',
          name: '边缘压缩',
          description: '数据压缩和解压缩处理',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '🗜️',
          color: '#1890ff',
          tags: ['压缩', '优化']
        },
        {
          type: 'EdgeOptimizationNode',
          name: '边缘优化',
          description: '边缘计算性能优化',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '⚡',
          color: '#1890ff',
          tags: ['优化', '性能']
        },
        {
          type: 'EdgeQoSNode',
          name: '边缘服务质量',
          description: '服务质量管理和控制',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '🎯',
          color: '#1890ff',
          tags: ['QoS', '质量']
        },

        // 云边协调节点 (8个)
        {
          type: 'CloudEdgeOrchestrationNode',
          name: '云边协调',
          description: '云端和边缘节点协调管理',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '☁️',
          color: '#52c41a',
          tags: ['协调', '管理']
        },
        {
          type: 'HybridComputingNode',
          name: '混合计算',
          description: '云边混合计算资源调度',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '🔄',
          color: '#52c41a',
          tags: ['混合', '计算']
        },
        {
          type: 'DataSynchronizationNode',
          name: '数据同步',
          description: '云边数据同步管理',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '🔄',
          color: '#52c41a',
          tags: ['同步', '数据']
        },
        {
          type: 'TaskDistributionNode',
          name: '任务分发',
          description: '计算任务智能分发',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '📤',
          color: '#52c41a',
          tags: ['分发', '任务']
        },
        {
          type: 'ResourceOptimizationNode',
          name: '资源优化',
          description: '云边资源优化配置',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '⚙️',
          color: '#52c41a',
          tags: ['资源', '优化']
        },
        {
          type: 'LatencyOptimizationNode',
          name: '延迟优化',
          description: '网络延迟优化管理',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '⏱️',
          color: '#52c41a',
          tags: ['延迟', '优化']
        },
        {
          type: 'BandwidthOptimizationNode',
          name: '带宽优化',
          description: '网络带宽优化管理',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '📊',
          color: '#52c41a',
          tags: ['带宽', '优化']
        },
        {
          type: 'CostOptimizationNode',
          name: '成本优化',
          description: '云边计算成本优化',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '💰',
          color: '#52c41a',
          tags: ['成本', '优化']
        },

        // 5G网络节点 (8个)
        {
          type: '5GConnectionNode',
          name: '5G连接',
          description: '5G网络连接管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '📶',
          color: '#722ed1',
          tags: ['5G', '连接']
        },
        {
          type: '5GSlicingNode',
          name: '5G网络切片',
          description: '5G网络切片管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '🍰',
          color: '#722ed1',
          tags: ['5G', '切片']
        },
        {
          type: '5GMECNode',
          name: '5G移动边缘计算',
          description: '5G MEC平台管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '🏗️',
          color: '#722ed1',
          tags: ['5G', 'MEC']
        },
        {
          type: '5GBeamformingNode',
          name: '5G波束成形',
          description: '5G波束成形优化',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '📡',
          color: '#722ed1',
          tags: ['5G', '波束成形']
        },
        {
          type: '5GMassiveMIMONode',
          name: '5G大规模MIMO',
          description: '5G大规模MIMO技术',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '📶',
          color: '#722ed1',
          tags: ['5G', 'MIMO']
        },
        {
          type: '5GURLLCNode',
          name: '5G超可靠低延迟',
          description: '5G URLLC服务管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '⚡',
          color: '#722ed1',
          tags: ['5G', 'URLLC']
        },
        {
          type: '5GeMBBNode',
          name: '5G增强移动宽带',
          description: '5G eMBB服务优化',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '🚀',
          color: '#722ed1',
          tags: ['5G', 'eMBB']
        },
        {
          type: '5GmMTCNode',
          name: '5G大规模机器通信',
          description: '5G mMTC物联网通信',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '🌐',
          color: '#722ed1',
          tags: ['5G', 'mMTC']
        },

        // 边缘设备管理节点 (25个)
        {
          type: 'EdgeDeviceRegistrationNode',
          name: '边缘设备注册',
          description: '边缘设备注册管理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '📝',
          color: '#fa8c16',
          tags: ['设备', '注册']
        },
        {
          type: 'EdgeDeviceMonitoringNode',
          name: '边缘设备监控',
          description: '边缘设备状态监控',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '📊',
          color: '#fa8c16',
          tags: ['设备', '监控']
        },
        {
          type: 'EdgeDeviceControlNode',
          name: '边缘设备控制',
          description: '边缘设备远程控制',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🎮',
          color: '#fa8c16',
          tags: ['设备', '控制']
        },
        {
          type: 'EdgeResourceManagementNode',
          name: '边缘资源管理',
          description: '边缘计算资源管理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '⚙️',
          color: '#fa8c16',
          tags: ['资源', '管理']
        },
        {
          type: 'EdgeNetworkNode',
          name: '边缘网络',
          description: '边缘网络连接管理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🌐',
          color: '#fa8c16',
          tags: ['网络', '连接']
        },
        {
          type: 'EdgeSecurityNode',
          name: '边缘安全',
          description: '边缘计算安全管理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🔒',
          color: '#fa8c16',
          tags: ['安全', '防护']
        },
        {
          type: 'EdgeUpdateNode',
          name: '边缘更新',
          description: '边缘设备软件更新',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🔄',
          color: '#fa8c16',
          tags: ['更新', '维护']
        },
        {
          type: 'EdgeDiagnosticsNode',
          name: '边缘诊断',
          description: '边缘设备故障诊断',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🔍',
          color: '#fa8c16',
          tags: ['诊断', '故障']
        },
        {
          type: 'EdgePerformanceNode',
          name: '边缘性能',
          description: '边缘设备性能监控',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '📈',
          color: '#fa8c16',
          tags: ['性能', '监控']
        },
        {
          type: 'EdgeFailoverNode',
          name: '边缘故障转移',
          description: '边缘设备故障转移',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🔄',
          color: '#fa8c16',
          tags: ['故障转移', '高可用']
        },
        {
          type: 'EdgeConfigurationNode',
          name: '边缘配置',
          description: '边缘设备配置管理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '⚙️',
          color: '#fa8c16',
          tags: ['配置', '管理']
        },
        {
          type: 'EdgeMaintenanceNode',
          name: '边缘维护',
          description: '边缘设备维护管理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🔧',
          color: '#fa8c16',
          tags: ['维护', '保养']
        },
        {
          type: 'EdgeBackupNode',
          name: '边缘备份',
          description: '边缘数据备份管理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '💾',
          color: '#fa8c16',
          tags: ['备份', '数据']
        },
        {
          type: 'EdgeSyncNode',
          name: '边缘同步',
          description: '边缘设备数据同步',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🔄',
          color: '#fa8c16',
          tags: ['同步', '数据']
        },
        {
          type: 'EdgeAnalyticsNode',
          name: '边缘分析',
          description: '边缘数据分析处理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '📊',
          color: '#fa8c16',
          tags: ['分析', '数据']
        },
        // 新增的7个边缘设备管理节点
        {
          type: 'EdgeDeviceHealthNode',
          name: '边缘设备健康',
          description: '边缘设备健康状态检查',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '💚',
          color: '#fa8c16',
          tags: ['健康', '状态']
        },
        {
          type: 'EdgeDeviceInventoryNode',
          name: '边缘设备库存',
          description: '边缘设备库存管理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '📦',
          color: '#fa8c16',
          tags: ['库存', '管理']
        },
        {
          type: 'EdgeDeviceAlertsNode',
          name: '边缘设备警报',
          description: '边缘设备异常警报',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🚨',
          color: '#fa8c16',
          tags: ['警报', '异常']
        },
        {
          type: 'EdgeDeviceLoggingNode',
          name: '边缘设备日志',
          description: '边缘设备日志记录',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '📝',
          color: '#fa8c16',
          tags: ['日志', '记录']
        },
        {
          type: 'EdgeDeviceRecoveryNode',
          name: '边缘设备恢复',
          description: '边缘设备故障恢复',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🔧',
          color: '#fa8c16',
          tags: ['恢复', '故障']
        },
        {
          type: 'EdgeDeviceComplianceNode',
          name: '边缘设备合规',
          description: '边缘设备合规检查',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '✅',
          color: '#fa8c16',
          tags: ['合规', '检查']
        },
        {
          type: 'EdgeDeviceIntegrationNode',
          name: '边缘设备集成',
          description: '边缘设备系统集成',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🔗',
          color: '#fa8c16',
          tags: ['集成', '系统']
        },
        {
          type: 'EdgeDeviceOrchestrationNode',
          name: '边缘设备编排',
          description: '边缘设备任务编排',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🎼',
          color: '#fa8c16',
          tags: ['编排', '任务']
        },
        {
          type: 'EdgeDeviceLifecycleNode',
          name: '边缘设备生命周期',
          description: '边缘设备生命周期管理',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '🔄',
          color: '#fa8c16',
          tags: ['生命周期', '管理']
        },
        {
          type: 'EdgeDeviceOptimizationNode',
          name: '边缘设备优化',
          description: '边缘设备性能优化',
          category: EdgeComputingNodeCategory.EDGE_DEVICE,
          icon: '⚡',
          color: '#fa8c16',
          tags: ['优化', '性能']
        },

        // 边缘AI节点 (12个)
        {
          type: 'EdgeAIInferenceNode',
          name: '边缘AI推理',
          description: '边缘设备AI模型推理',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '🧠',
          color: '#eb2f96',
          tags: ['AI', '推理']
        },
        {
          type: 'EdgeModelDeploymentNode',
          name: '边缘模型部署',
          description: 'AI模型边缘部署管理',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '🚀',
          color: '#eb2f96',
          tags: ['模型', '部署']
        },
        {
          type: 'EdgeModelOptimizationNode',
          name: '边缘模型优化',
          description: '边缘AI模型优化',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '⚡',
          color: '#eb2f96',
          tags: ['模型', '优化']
        },
        {
          type: 'EdgeFederatedLearningNode',
          name: '边缘联邦学习',
          description: '边缘联邦学习协调',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '🤝',
          color: '#eb2f96',
          tags: ['联邦学习', '协作']
        },
        {
          type: 'EdgeAIMonitoringNode',
          name: '边缘AI监控',
          description: '边缘AI服务监控',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '📊',
          color: '#eb2f96',
          tags: ['AI', '监控']
        },
        {
          type: 'EdgeAIPerformanceNode',
          name: '边缘AI性能',
          description: '边缘AI性能分析',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '📈',
          color: '#eb2f96',
          tags: ['AI', '性能']
        },
        {
          type: 'EdgeAISecurityNode',
          name: '边缘AI安全',
          description: '边缘AI安全防护',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '🔒',
          color: '#eb2f96',
          tags: ['AI', '安全']
        },
        {
          type: 'EdgeAIAnalyticsNode',
          name: '边缘AI分析',
          description: '边缘AI数据分析',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '📊',
          color: '#eb2f96',
          tags: ['AI', '分析']
        },
        {
          type: 'EdgeModelCacheNode',
          name: '边缘模型缓存',
          description: '边缘AI模型缓存管理',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '💾',
          color: '#eb2f96',
          tags: ['模型', '缓存']
        },
        {
          type: 'EdgeAIResourceNode',
          name: '边缘AI资源',
          description: '边缘AI资源管理',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '⚙️',
          color: '#eb2f96',
          tags: ['AI', '资源']
        },
        {
          type: 'EdgeAISchedulerNode',
          name: '边缘AI调度',
          description: '边缘AI任务调度',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '📅',
          color: '#eb2f96',
          tags: ['AI', '调度']
        },
        {
          type: 'EdgeModelVersioningNode',
          name: '边缘模型版本',
          description: '边缘AI模型版本管理',
          category: EdgeComputingNodeCategory.EDGE_AI,
          icon: '🔢',
          color: '#eb2f96',
          tags: ['模型', '版本']
        }
      ];

      setNodeItems(nodes);
      setFilteredNodes(nodes);
    };

    initializeNodes();
  }, []);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    filterNodes(value, selectedCategory);
  };

  // 处理分类过滤
  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category);
    filterNodes(searchText, category);
  };

  // 过滤节点
  const filterNodes = (search: string, category: string) => {
    let filtered = nodeItems;

    // 分类过滤
    if (category !== 'all') {
      filtered = filtered.filter(node => node.category === category);
    }

    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(node =>
        node.name.toLowerCase().includes(searchLower) ||
        node.description.toLowerCase().includes(searchLower) ||
        node.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    setFilteredNodes(filtered);
  };

  // 处理节点点击
  const handleNodeClick = (node: NodeItem) => {
    if (onNodeSelect) {
      onNodeSelect(node.type);
    }
  };

  // 处理节点添加
  const handleNodeAdd = (node: NodeItem) => {
    if (onNodeAdd) {
      onNodeAdd(node.type);
    }
  };

  // 处理节点信息查看
  const handleNodeInfo = (node: NodeItem) => {
    setSelectedNode(node);
    setShowProperties(true);

    // 创建默认属性
    const defaultProperties: EdgeComputingNodeProperties = {
      nodeId: `${node.type}_${Date.now()}`,
      nodeName: node.name,
      nodeType: node.type,
      category: node.category,
      description: node.description,
      endpoint: 'http://localhost:8080',
      port: 8080,
      protocol: 'http',
      timeout: 30,
      retryCount: 3,
      cpuLimit: 50,
      memoryLimit: 2,
      storageLimit: 100,
      bandwidth: 1000,
      enableSecurity: false,
      authMethod: 'none',
      encryptionEnabled: false,
      enableMonitoring: true,
      metricsInterval: 30,
      alertThreshold: 80,
      logLevel: 'info',
      tags: node.tags,
      metadata: {}
    };

    setNodeProperties(defaultProperties);
  };

  // 处理拖拽开始
  const handleDragStart = (node: NodeItem) => {
    console.log('开始拖拽节点:', node.name);
  };

  // 处理拖拽结束
  const handleDragEnd = (node: NodeItem) => {
    console.log('结束拖拽节点:', node.name);
  };

  // 处理节点放置
  const handleNodeDrop = (dragItem: EdgeComputingDragItem, position: { x: number; y: number }) => {
    if (onNodeDrop) {
      onNodeDrop(dragItem, position);
    }
  };

  // 处理属性变化
  const handlePropertiesChange = (properties: EdgeComputingNodeProperties) => {
    setNodeProperties(properties);
    if (selectedNode && onNodePropertiesChange) {
      onNodePropertiesChange(selectedNode.type, properties);
    }
  };

  // 处理属性保存
  const handlePropertiesSave = (properties: EdgeComputingNodeProperties) => {
    setNodeProperties(properties);
    if (selectedNode && onNodePropertiesChange) {
      onNodePropertiesChange(selectedNode.type, properties);
    }
    message.success('节点属性已保存');
  };

  // 处理属性重置
  const handlePropertiesReset = () => {
    if (selectedNode) {
      handleNodeInfo(selectedNode);
    }
  };

  // 处理拖拽开始
  const handleDragStart = (node: NodeItem) => {
    console.log('开始拖拽节点:', node.name);
  };

  // 处理拖拽结束
  const handleDragEnd = (node: NodeItem) => {
    console.log('结束拖拽节点:', node.name);
  };

  // 处理节点放置
  const handleNodeDrop = (dragItem: EdgeComputingDragItem, position: { x: number; y: number }) => {
    if (onNodeDrop) {
      onNodeDrop(dragItem, position);
    }
  };

  // 处理属性变化
  const handlePropertiesChange = (properties: EdgeComputingNodeProperties) => {
    setNodeProperties(properties);
    if (selectedNode && onNodePropertiesChange) {
      onNodePropertiesChange(selectedNode.type, properties);
    }
  };

  // 处理属性保存
  const handlePropertiesSave = (properties: EdgeComputingNodeProperties) => {
    setNodeProperties(properties);
    if (selectedNode && onNodePropertiesChange) {
      onNodePropertiesChange(selectedNode.type, properties);
    }
    message.success('节点属性已保存');
  };

  // 处理属性重置
  const handlePropertiesReset = () => {
    if (selectedNode) {
      handleNodeInfo(selectedNode);
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <CloudServerOutlined />
          <Title level={4} style={{ margin: 0 }}>
            边缘计算节点
          </Title>
          <Badge count={filteredNodes.length} style={{ backgroundColor: '#1890ff' }} />
        </Space>
      }
      size="small"
      style={{ height: '100%', overflow: 'hidden' }}
      bodyStyle={{ padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' }}
    >
      <Spin spinning={loading}>
        {/* 搜索和过滤 */}
        <Space direction="vertical" style={{ width: '100%', marginBottom: 12 }}>
          <Search
            placeholder="搜索边缘计算节点..."
            allowClear
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            prefix={<SearchOutlined />}
          />
          
          <Space wrap>
            <Button
              size="small"
              type={selectedCategory === 'all' ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter('all')}
            >
              全部
            </Button>
            <Button
              size="small"
              type={selectedCategory === EdgeComputingNodeCategory.EDGE_ROUTING ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter(EdgeComputingNodeCategory.EDGE_ROUTING)}
            >
              边缘路由
            </Button>
            <Button
              size="small"
              type={selectedCategory === EdgeComputingNodeCategory.CLOUD_EDGE ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter(EdgeComputingNodeCategory.CLOUD_EDGE)}
            >
              云边协调
            </Button>
            <Button
              size="small"
              type={selectedCategory === EdgeComputingNodeCategory.FIVE_G ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter(EdgeComputingNodeCategory.FIVE_G)}
            >
              5G网络
            </Button>
            <Button
              size="small"
              type={selectedCategory === EdgeComputingNodeCategory.EDGE_DEVICE ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter(EdgeComputingNodeCategory.EDGE_DEVICE)}
            >
              边缘设备
            </Button>
            <Button
              size="small"
              type={selectedCategory === EdgeComputingNodeCategory.EDGE_AI ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter(EdgeComputingNodeCategory.EDGE_AI)}
            >
              边缘AI
            </Button>
          </Space>
        </Space>

        <Divider style={{ margin: '8px 0' }} />

        {/* 节点列表 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          {filteredNodes.map((node) => (
            <EdgeComputingNodeDragDrop
              key={node.type}
              node={{
                type: node.type,
                name: node.name,
                description: node.description,
                category: node.category,
                icon: node.icon,
                color: node.color,
                tags: node.tags
              }}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              onNodeAdd={handleNodeAdd}
              onNodeInfo={handleNodeInfo}
              showActions={true}
            />
          ))}
        </div>

        {/* 属性编辑器 */}
        {showPropertyEditor && showProperties && selectedNode && nodeProperties && (
          <div style={{ marginTop: 16 }}>
            <EdgeComputingNodePropertyEditor
              nodeType={selectedNode.type}
              properties={nodeProperties}
              onPropertiesChange={handlePropertiesChange}
              onSave={handlePropertiesSave}
              onReset={handlePropertiesReset}
              visible={showProperties}
            />
          </div>
        )}
      </Spin>
    </Card>
  );
};

export default EdgeComputingNodesPanel;
