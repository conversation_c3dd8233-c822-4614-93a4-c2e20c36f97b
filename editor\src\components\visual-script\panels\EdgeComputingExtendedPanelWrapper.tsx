/**
 * 边缘计算扩展面板包装器
 * 集成批次5：边缘计算扩展面板（59个节点）演示组件
 */

import React, { useState, useCallback } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { 
  Layout, 
  Card, 
  Space, 
  Typography, 
  Statistic, 
  Row, 
  Col, 
  Alert, 
  Button,
  message,
  Divider
} from 'antd';
import {
  CloudServerOutlined,
  ApiOutlined,
  WifiOutlined,
  MobileOutlined,
  BrainOutlined,
  DragOutlined,
  SettingOutlined
} from '@ant-design/icons';
import EdgeComputingNodesPanel from './EdgeComputingNodesPanel';
import { EdgeComputingDropZone, EdgeComputingDragItem } from '../nodes/EdgeComputingNodeDragDrop';
import { EdgeComputingNodeProperties } from '../nodes/EdgeComputingNodePropertyEditor';

const { Content, Sider } = Layout;
const { Title, Text } = Typography;

/**
 * 边缘计算扩展面板包装器组件
 */
export const EdgeComputingExtendedPanelWrapper: React.FC = () => {
  const [droppedNodes, setDroppedNodes] = useState<EdgeComputingDragItem[]>([]);
  const [nodeProperties, setNodeProperties] = useState<Record<string, EdgeComputingNodeProperties>>({});
  const [selectedNodeType, setSelectedNodeType] = useState<string | null>(null);

  // 处理节点选择
  const handleNodeSelect = useCallback((nodeType: string) => {
    setSelectedNodeType(nodeType);
    console.log('选择节点:', nodeType);
  }, []);

  // 处理节点添加
  const handleNodeAdd = useCallback((nodeType: string) => {
    const newNode: EdgeComputingDragItem = {
      type: nodeType,
      name: getNodeDisplayName(nodeType),
      description: getNodeDescription(nodeType),
      category: getNodeCategory(nodeType),
      icon: getNodeIcon(nodeType),
      color: getNodeColor(nodeType),
      tags: getNodeTags(nodeType)
    };
    
    setDroppedNodes(prev => [...prev, newNode]);
    message.success(`已添加 ${newNode.name} 到画布`);
  }, []);

  // 处理节点拖拽放置
  const handleNodeDrop = useCallback((node: EdgeComputingDragItem, position: { x: number; y: number }) => {
    const droppedNode = {
      ...node,
      nodeData: {
        position,
        id: `${node.type}_${Date.now()}`
      }
    };
    
    setDroppedNodes(prev => [...prev, droppedNode]);
    message.success(`已将 ${node.name} 放置到画布 (${position.x}, ${position.y})`);
  }, []);

  // 处理节点属性变化
  const handleNodePropertiesChange = useCallback((nodeType: string, properties: EdgeComputingNodeProperties) => {
    setNodeProperties(prev => ({
      ...prev,
      [nodeType]: properties
    }));
  }, []);

  // 清空画布
  const handleClearCanvas = useCallback(() => {
    setDroppedNodes([]);
    setNodeProperties({});
    message.info('画布已清空');
  }, []);

  // 获取节点显示名称
  const getNodeDisplayName = (nodeType: string): string => {
    const nameMap: Record<string, string> = {
      'EdgeRoutingNode': '边缘路由',
      'EdgeLoadBalancingNode': '边缘负载均衡',
      'EdgeCachingNode': '边缘缓存',
      'EdgeCompressionNode': '边缘压缩',
      'EdgeOptimizationNode': '边缘优化',
      'EdgeQoSNode': '边缘服务质量',
      'CloudEdgeOrchestrationNode': '云边协调',
      'HybridComputingNode': '混合计算',
      'DataSynchronizationNode': '数据同步',
      'TaskDistributionNode': '任务分发',
      'ResourceOptimizationNode': '资源优化',
      'LatencyOptimizationNode': '延迟优化',
      'BandwidthOptimizationNode': '带宽优化',
      'CostOptimizationNode': '成本优化',
      '5GConnectionNode': '5G连接',
      '5GSlicingNode': '5G网络切片',
      '5GMECNode': '5G移动边缘计算',
      '5GBeamformingNode': '5G波束成形',
      '5GMassiveMIMONode': '5G大规模MIMO',
      '5GURLLCNode': '5G超可靠低延迟',
      '5GeMBBNode': '5G增强移动宽带',
      '5GmMTCNode': '5G大规模机器通信',
      'EdgeDeviceRegistrationNode': '边缘设备注册',
      'EdgeDeviceMonitoringNode': '边缘设备监控',
      'EdgeDeviceControlNode': '边缘设备控制',
      'EdgeResourceManagementNode': '边缘资源管理',
      'EdgeNetworkNode': '边缘网络',
      'EdgeSecurityNode': '边缘安全',
      'EdgeUpdateNode': '边缘更新',
      'EdgeDiagnosticsNode': '边缘诊断',
      'EdgePerformanceNode': '边缘性能',
      'EdgeFailoverNode': '边缘故障转移',
      'EdgeConfigurationNode': '边缘配置',
      'EdgeMaintenanceNode': '边缘维护',
      'EdgeBackupNode': '边缘备份',
      'EdgeSyncNode': '边缘同步',
      'EdgeAnalyticsNode': '边缘分析',
      'EdgeAIInferenceNode': '边缘AI推理',
      'EdgeModelDeploymentNode': '边缘模型部署',
      'EdgeModelOptimizationNode': '边缘模型优化',
      'EdgeFederatedLearningNode': '边缘联邦学习',
      'EdgeAIMonitoringNode': '边缘AI监控',
      'EdgeAIPerformanceNode': '边缘AI性能',
      'EdgeAISecurityNode': '边缘AI安全',
      'EdgeAIAnalyticsNode': '边缘AI分析',
      'EdgeModelCacheNode': '边缘模型缓存'
    };
    return nameMap[nodeType] || nodeType;
  };

  // 获取节点描述
  const getNodeDescription = (nodeType: string): string => {
    return `${getNodeDisplayName(nodeType)}节点的功能描述`;
  };

  // 获取节点分类
  const getNodeCategory = (nodeType: string): string => {
    if (nodeType.includes('5G')) return 'Edge/5G';
    if (nodeType.includes('AI') || nodeType.includes('Model') || nodeType.includes('Federated')) return 'Edge/AI';
    if (nodeType.includes('Device') || nodeType.includes('Edge')) return 'Edge/Device';
    if (nodeType.includes('Cloud') || nodeType.includes('Hybrid') || nodeType.includes('Data') || nodeType.includes('Task')) return 'Edge/CloudEdge';
    return 'Edge/Routing';
  };

  // 获取节点图标
  const getNodeIcon = (nodeType: string): string => {
    if (nodeType.includes('5G')) return '📶';
    if (nodeType.includes('AI') || nodeType.includes('Model')) return '🧠';
    if (nodeType.includes('Device')) return '📱';
    if (nodeType.includes('Cloud')) return '☁️';
    return '🔀';
  };

  // 获取节点颜色
  const getNodeColor = (nodeType: string): string => {
    if (nodeType.includes('5G')) return '#722ed1';
    if (nodeType.includes('AI') || nodeType.includes('Model')) return '#eb2f96';
    if (nodeType.includes('Device')) return '#fa8c16';
    if (nodeType.includes('Cloud')) return '#52c41a';
    return '#1890ff';
  };

  // 获取节点标签
  const getNodeTags = (nodeType: string): string[] => {
    const tags = [];
    if (nodeType.includes('5G')) tags.push('5G');
    if (nodeType.includes('AI')) tags.push('AI');
    if (nodeType.includes('Device')) tags.push('设备');
    if (nodeType.includes('Cloud')) tags.push('云端');
    if (nodeType.includes('Edge')) tags.push('边缘');
    return tags;
  };

  // 统计信息
  const statistics = {
    totalNodes: 59,
    edgeRoutingNodes: 6,
    cloudEdgeNodes: 8,
    fiveGNodes: 8,
    edgeDeviceNodes: 25,
    edgeAINodes: 12,
    droppedNodes: droppedNodes.length
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div style={{ height: '100vh', padding: '16px' }}>
        <Title level={3} style={{ marginBottom: 16 }}>
          <Space>
            <CloudServerOutlined />
            集成批次5：边缘计算扩展面板（59个节点）
          </Space>
        </Title>

        <Alert
          message="边缘计算扩展面板集成完成"
          description="包含边缘路由、云边协调、5G网络、边缘设备管理、边缘AI等5个子面板，共59个节点，支持拖拽和属性编辑功能。"
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* 统计信息 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={4}>
            <Statistic
              title="总节点数"
              value={statistics.totalNodes}
              prefix={<CloudServerOutlined />}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="边缘路由"
              value={statistics.edgeRoutingNodes}
              prefix={<ApiOutlined />}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="云边协调"
              value={statistics.cloudEdgeNodes}
              prefix={<CloudServerOutlined />}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="5G网络"
              value={statistics.fiveGNodes}
              prefix={<WifiOutlined />}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="边缘设备"
              value={statistics.edgeDeviceNodes}
              prefix={<MobileOutlined />}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="边缘AI"
              value={statistics.edgeAINodes}
              prefix={<BrainOutlined />}
            />
          </Col>
        </Row>

        <Layout style={{ height: 'calc(100vh - 200px)', background: '#fff' }}>
          {/* 节点面板 */}
          <Sider width={350} style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}>
            <EdgeComputingNodesPanel
              onNodeSelect={handleNodeSelect}
              onNodeAdd={handleNodeAdd}
              onNodeDrop={handleNodeDrop}
              onNodePropertiesChange={handleNodePropertiesChange}
              visible={true}
              loading={false}
              showPropertyEditor={true}
            />
          </Sider>

          {/* 画布区域 */}
          <Content style={{ padding: '16px' }}>
            <Card
              title={
                <Space>
                  <DragOutlined />
                  <span>拖拽画布</span>
                  <Text type="secondary">({statistics.droppedNodes} 个节点)</Text>
                </Space>
              }
              extra={
                <Button onClick={handleClearCanvas} disabled={droppedNodes.length === 0}>
                  清空画布
                </Button>
              }
              style={{ height: '100%' }}
              bodyStyle={{ height: 'calc(100% - 57px)', overflow: 'auto' }}
            >
              <EdgeComputingDropZone
                onDrop={handleNodeDrop}
                style={{
                  height: '100%',
                  minHeight: '400px',
                  border: '2px dashed #d9d9d9',
                  borderRadius: '8px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#fafafa'
                }}
              >
                {droppedNodes.length === 0 ? (
                  <div style={{ textAlign: 'center', color: '#999' }}>
                    <DragOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <div>将边缘计算节点拖拽到此处</div>
                    <div style={{ fontSize: '12px', marginTop: '8px' }}>
                      支持边缘路由、云边协调、5G网络、边缘设备、边缘AI等节点
                    </div>
                  </div>
                ) : (
                  <div style={{ width: '100%', padding: '16px' }}>
                    <Title level={5}>已添加的节点:</Title>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                      {droppedNodes.map((node, index) => (
                        <Card
                          key={index}
                          size="small"
                          style={{ 
                            minWidth: '200px',
                            border: `2px solid ${node.color}`,
                            borderRadius: '8px'
                          }}
                        >
                          <Space>
                            <div
                              style={{
                                width: 24,
                                height: 24,
                                borderRadius: '50%',
                                backgroundColor: node.color,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'white',
                                fontSize: '12px'
                              }}
                            >
                              {node.icon}
                            </div>
                            <div>
                              <div style={{ fontWeight: 'bold', fontSize: '13px' }}>
                                {node.name}
                              </div>
                              <div style={{ fontSize: '11px', color: '#666' }}>
                                {node.type}
                              </div>
                            </div>
                          </Space>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </EdgeComputingDropZone>
            </Card>
          </Content>
        </Layout>
      </div>
    </DndProvider>
  );
};

export default EdgeComputingExtendedPanelWrapper;
